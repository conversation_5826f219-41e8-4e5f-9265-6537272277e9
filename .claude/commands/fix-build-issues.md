We are trying to clear out the many lint and typecheck issues in the project.
Please use the lint and typecheck scripts to resolve all issues.
Do not introduce any lint of type issues with your changes. For example, DO NOT use type any!

For database schema interfaces, I believe drizzle has a built in function for inferring the types.
Think harder. Ensure you don't introduce new type and lint errors with your changes.
