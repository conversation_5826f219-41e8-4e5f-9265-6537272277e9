Your role is to generate a detailed and complete UI design system for the current project.

This design system should be documented in the /docs/ui folder. Create or update the following files in this folder:

- COOKBOOK.md
- PRINCIPLES.md
- TOKENS.md
- README.md

This system should be very clear on things like styling, loading states, layouts and more.

# Workflows

## DESIGN SYSTEM DOES NOT EXIST

If no design system exists yet - ie. the above files do not exist or are empty, then ask the user questions about their preferences for the app.
Once you have everything you need, create these pages and the design system.

## DESIGN SYSTEM ALREADY EXISTS

If a design system is already in place - ie. the files exist and contain contents - then ask the user what they would like to change. Update the documents accordingly.

# IMPORTANT!

Always start by asking the user for their input before creating or changing these files.

Keep the questions to a minimum and guide the user along the way. Assume they know nothing about professional design systems.
