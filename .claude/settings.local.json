{"permissions": {"allow": ["mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npm run lint)", "Bash(npm run typecheck:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_close", "Bash(git add:*)", "Bash(git log:*)"], "additionalDirectories": ["C:\\c\\Projects\\nextjs-better-auth-postgresql-starter-kit"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["context7"]}