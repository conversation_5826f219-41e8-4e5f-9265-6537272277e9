{"name": "agentic-coding-boilerplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "pnpm run db:migrate && next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:dev": "drizzle-kit push", "db:reset": "drizzle-kit drop && drizzle-kit push"}, "dependencies": {"@ai-sdk/openai": "^2.0.9", "@ai-sdk/react": "^2.0.9", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-use-controllable-state": "^1.2.2", "ai": "^5.0.51", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.4", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.544.0", "nanoid": "^5.1.6", "next": "15.4.6", "next-themes": "^0.4.6", "pg": "^8.16.3", "postgres": "^3.4.7", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.6", "streamdown": "^1.3.0", "tailwind-merge": "^3.3.1", "tokenlens": "^1.3.1", "use-stick-to-bottom": "^1.1.1", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.6", "shadcn": "^3.0.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}