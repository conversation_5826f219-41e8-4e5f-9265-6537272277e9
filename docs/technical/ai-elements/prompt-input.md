# Prompt Input Component

## Description
The Prompt Input component provides an enhanced input field for chat interfaces, with features like auto-resize, placeholder text, and integration with chat functionality.

## Installation
```bash
npx ai-elements@latest add prompt-input
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/prompt-input.json
```

## Usage
```tsx
import { PromptInput } from '@/components/ai-elements/prompt-input';

function ChatInterface() {
  const [input, setInput] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (input.trim()) {
      sendMessage(input);
      setInput('');
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <PromptInput
        value={input}
        onChange={setInput}
        placeholder="Type your message..."
        disabled={isLoading}
      />
    </form>
  );
}
```

## Props
- `value: string` - Input value
- `onChange: (value: string) => void` - Change handler
- `placeholder?: string` - Placeholder text
- `disabled?: boolean` - Disable input
- `maxLength?: number` - Maximum character limit
- `autoFocus?: boolean` - Auto-focus on mount