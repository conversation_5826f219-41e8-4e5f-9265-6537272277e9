# Response Component

## Description
The Response component handles the display of AI-generated responses, including support for streaming text, formatting, and interactive elements within responses.

## Installation
```bash
npx ai-elements@latest add response
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/response.json
```

## Usage
```tsx
import { Response } from '@/components/ai-elements/response';

function ChatMessage({ message }) {
  return (
    <Message from="assistant">
      <MessageContent>
        <Response>{message.content}</Response>
      </MessageContent>
    </Message>
  );
}
```

## Props
- `children: ReactNode` - Response content
- `className?: string` - Additional CSS classes
- `streaming?: boolean` - Enable streaming animation
- `onComplete?: () => void` - Completion callback
- `format?: 'text' | 'markdown' | 'html'` - Content format