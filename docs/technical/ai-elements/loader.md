# Loader Component

## Description
The Loader component provides animated loading indicators for chat messages, streaming responses, and other async operations in AI interfaces.

## Installation
```bash
npx ai-elements@latest add loader
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/loader.json
```

## Usage
```tsx
import { Loader } from '@/components/ai-elements/loader';

function ChatMessage({ message, isLoading }) {
  return (
    <div className="flex items-center gap-2">
      <span>{message.role}:</span>
      {isLoading ? (
        <Loader size="sm" />
      ) : (
        <span>{message.content}</span>
      )}
    </div>
  );
}
```

## Props
- `size?: 'sm' | 'md' | 'lg'` - Size of the loader (default: 'md')
- `className?: string` - Additional CSS classes
- `type?: 'spinner' | 'dots' | 'pulse'` - Animation type (default: 'spinner')