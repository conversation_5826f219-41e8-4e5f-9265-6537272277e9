# Message Component

## Description
The Message component represents individual chat messages, providing consistent styling and layout for both user and AI messages in conversations.

## Installation
```bash
npx ai-elements@latest add message
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/message.json
```

## Usage
```tsx
import {
  Message,
  MessageContent,
} from '@/components/ai-elements/message';

function ChatMessage({ message }) {
  return (
    <Message from={message.role}>
      <MessageContent>
        {message.content}
      </MessageContent>
    </Message>
  );
}
```

## Props
- `from: 'user' | 'assistant'` - Message sender type
- `children?: ReactNode` - Message content
- `className?: string` - Additional CSS classes
- `timestamp?: Date` - Message timestamp
- `avatar?: ReactNode` - Custom avatar component