# Inline Citation Component

## Description
The Inline Citation component displays source citations directly within chat messages, providing clickable references to the original sources of information.

## Installation
```bash
npx ai-elements@latest add inline-citation
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/inline-citation.json
```

## Usage
```tsx
import { InlineCitation } from '@/components/ai-elements/inline-citation';

function ChatMessage({ message }) {
  return (
    <div>
      {message.content}
      {message.citations && message.citations.map((citation, index) => (
        <InlineCitation
          key={index}
          citation={citation}
          onClick={() => window.open(citation.url, '_blank')}
        />
      ))}
    </div>
  );
}
```

## Props
- `citation: object` - Citation object containing title, url, and metadata
- `onClick?: () => void` - Click handler for citation
- `className?: string` - Additional CSS classes
- `showTooltip?: boolean` - Display tooltip with citation details