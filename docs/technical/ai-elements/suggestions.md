# Suggestions Component

## Description
The Suggestions component provides clickable suggestion chips or buttons that help users get started with common queries or continue conversations with relevant prompts.

## Installation
```bash
npx ai-elements@latest add suggestions
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/suggestions.json
```

## Usage
```tsx
import { Suggestions } from '@/components/ai-elements/suggestions';

function ChatInterface() {
  const suggestions = [
    "Explain quantum computing",
    "Write a Python function",
    "Help with React hooks",
    "Database optimization tips"
  ];

  return (
    <div>
      <Suggestions
        items={suggestions}
        onSelect={(suggestion) => sendMessage(suggestion)}
      />
    </div>
  );
}
```

## Props
- `items: Array<string>` - Array of suggestion strings
- `onSelect?: (item: string) => void` - Selection handler
- `className?: string` - Additional CSS classes
- `maxItems?: number` - Maximum number of suggestions to show
- `layout?: 'horizontal' | 'vertical' | 'grid'` - Layout style