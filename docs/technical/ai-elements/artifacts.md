# Artifacts Component

## Description
The Artifacts component displays generated content such as code snippets, documents, or other structured outputs from AI responses in a dedicated, interactive panel.

## Installation
```bash
npx ai-elements@latest add artifacts
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/artifacts.json
```

## Usage
```tsx
import { Artifacts } from '@/components/ai-elements/artifacts';

function ChatInterface() {
  const [artifacts, setArtifacts] = useState([]);

  return (
    <div className="flex">
      <div className="flex-1">
        {/* Chat messages */}
      </div>
      <Artifacts
        artifacts={artifacts}
        onArtifactSelect={(artifact) => console.log('Selected:', artifact)}
        onArtifactClose={(id) => setArtifacts(prev => prev.filter(a => a.id !== id))}
      />
    </div>
  );
}
```

## Props
- `artifacts?: Array` - Array of artifact objects to display
- `onArtifactSelect?: (artifact) => void` - Handler for artifact selection
- `onArtifactClose?: (id) => void` - Handler for closing an artifact
- `className?: string` - Additional CSS classes