# Sources Component

## Description
The Sources component displays a list of source references and citations that the AI used to generate its response, typically shown at the bottom of messages or in a dedicated panel.

## Installation
```bash
npx ai-elements@latest add sources
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/sources.json
```

## Usage
```tsx
import { Sources } from '@/components/ai-elements/sources';

function ChatMessage({ message }) {
  return (
    <div>
      <div>{message.content}</div>
      <Sources
        sources={message.sources}
        onSourceClick={(source) => window.open(source.url, '_blank')}
      />
    </div>
  );
}
```

## Props
- `sources: Array` - Array of source objects
- `onSourceClick?: (source) => void` - Source click handler
- `className?: string` - Additional CSS classes
- `layout?: 'list' | 'grid' | 'compact'` - Display layout
- `showThumbnails?: boolean` - Display source thumbnails