# Tool Component

## Description
The Tool component displays information about AI tools and functions that are being used or called during conversations, including tool execution status and results.

## Installation
```bash
npx ai-elements@latest add tool
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/tool.json
```

## Usage
```tsx
import { Tool } from '@/components/ai-elements/tool';

function ChatMessage({ message }) {
  return (
    <div>
      {message.content}
      {message.toolCalls && message.toolCalls.map((toolCall, index) => (
        <Tool
          key={index}
          toolCall={toolCall}
          onResult={(result) => handleToolResult(toolCall.id, result)}
        />
      ))}
    </div>
  );
}
```

## Props
- `toolCall: object` - Tool call object with name, parameters, status
- `onResult?: (result) => void` - Tool result handler
- `className?: string` - Additional CSS classes
- `showDetails?: boolean` - Display detailed tool information
- `collapsible?: boolean` - Allow collapsing tool details