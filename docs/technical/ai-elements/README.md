# AI Elements Chat Interface Components

This folder contains documentation for Vercel's AI Elements components, a customizable React component library built on shadcn/ui for building AI-native applications.

## Overview

AI Elements provides pre-built, customizable components specifically designed for AI chat interfaces and conversational applications. Each component is designed to work seamlessly with the Vercel AI SDK and follows modern React patterns.

## Components

- **Actions** - Interactive buttons and controls for chat messages
- **Artifacts** - Display panel for generated content like code snippets
- **Conversation** - Main container for chat interfaces
- **Image** - Image display and interaction component
- **Inline Citation** - Source citations within messages
- **Loader** - Loading indicators for async operations
- **Message** - Individual chat message component
- **Prompt Input** - Enhanced input field for chat
- **Reasoning** - AI reasoning process display
- **Response** - AI response rendering component
- **Sources** - Source references and citations panel
- **Suggestions** - Clickable suggestion chips
- **Task** - Actionable task management
- **Tool** - AI tool execution display
- **Web Preview** - Embedded web content preview

## Installation

### Option 1: Install All Components
```bash
npx ai-elements@latest
```

### Option 2: Install Individual Components
```bash
npx ai-elements@latest add <component-name>
```

For example:
```bash
npx ai-elements@latest add message
npx ai-elements@latest add conversation
npx ai-elements@latest add response
```

### Option 3: Using shadcn CLI
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/all.json
```

Or for individual components:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/message.json
```

## Prerequisites

- Node.js 18+
- Next.js project (or React with Vite)
- AI SDK installed: `npm install ai`
- shadcn/ui initialized
- Tailwind CSS configured

## Basic Usage

```tsx
'use client';

import { useChat } from '@ai-sdk/react';
import {
  Conversation,
  ConversationContent,
} from '@/components/ai-elements/conversation';
import {
  Message,
  MessageContent,
} from '@/components/ai-elements/message';
import { Response } from '@/components/ai-elements/response';

export default function Chat() {
  const { messages } = useChat();

  return (
    <Conversation>
      <ConversationContent>
        {messages.map((message, index) => (
          <Message key={index} from={message.role}>
            <MessageContent>
              <Response>{message.content}</Response>
            </MessageContent>
          </Message>
        ))}
      </ConversationContent>
    </Conversation>
  );
}
```

## Integration with AI SDK

These components are designed to work seamlessly with the Vercel AI SDK's `useChat` hook and streaming responses. They handle various message types including text, images, tool calls, and structured data.

## Customization

All components accept a `className` prop for custom styling and can be extended or modified to fit your design system. They follow shadcn/ui patterns for consistent theming and customization.