# Image Component

## Description
The Image component handles the display and interaction of images within chat messages, including support for lightbox viewing, zooming, and image manipulation.

## Installation
```bash
npx ai-elements@latest add image
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/image.json
```

## Usage
```tsx
import { Image } from '@/components/ai-elements/image';

function ChatMessage({ message }) {
  if (message.type === 'image') {
    return (
      <Image
        src={message.url}
        alt={message.alt}
        width={message.width}
        height={message.height}
        onClick={() => openLightbox(message.url)}
      />
    );
  }

  return <div>{message.content}</div>;
}
```

## Props
- `src: string` - Image source URL
- `alt?: string` - Alt text for accessibility
- `width?: number` - Image width
- `height?: number` - Image height
- `onClick?: () => void` - Click handler for image interaction
- `className?: string` - Additional CSS classes
- `lightbox?: boolean` - Enable lightbox functionality