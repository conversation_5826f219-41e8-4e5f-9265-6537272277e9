# Reasoning Component

## Description
The Reasoning component displays the AI's thought process and reasoning steps, often shown as expandable sections or inline explanations within chat messages.

## Installation
```bash
npx ai-elements@latest add reasoning
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/reasoning.json
```

## Usage
```tsx
import { Reasoning } from '@/components/ai-elements/reasoning';

function ChatMessage({ message }) {
  return (
    <div>
      <div>{message.content}</div>
      {message.reasoning && (
        <Reasoning
          steps={message.reasoning}
          collapsed={false}
          onToggle={(collapsed) => console.log('Toggled:', collapsed)}
        />
      )}
    </div>
  );
}
```

## Props
- `steps: Array` - Array of reasoning steps
- `collapsed?: boolean` - Initial collapsed state
- `onToggle?: (collapsed: boolean) => void` - Toggle handler
- `className?: string` - Additional CSS classes
- `showStepNumbers?: boolean` - Display step numbers