# Actions Component

## Description
The Actions component provides interactive buttons and controls for AI chat interfaces, enabling users to perform actions like copying, editing, or regenerating responses.

## Installation
```bash
npx ai-elements@latest add actions
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/actions.json
```

## Usage
```tsx
import { Actions } from '@/components/ai-elements/actions';

function ChatMessage({ message }) {
  return (
    <div className="flex justify-between items-start">
      <div>{message.content}</div>
      <Actions
        onCopy={() => navigator.clipboard.writeText(message.content)}
        onEdit={() => handleEdit(message.id)}
        onRegenerate={() => handleRegenerate(message.id)}
      />
    </div>
  );
}
```

## Props
- `onCopy?: () => void` - Handler for copy action
- `onEdit?: () => void` - Handler for edit action
- `onRegenerate?: () => void` - Handler for regenerate action
- `className?: string` - Additional CSS classes