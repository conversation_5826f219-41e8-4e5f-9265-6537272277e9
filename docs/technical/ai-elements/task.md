# Task Component

## Description
The Task component represents actionable items or tasks that can be created from chat conversations, with support for status tracking, progress indicators, and task management.

## Installation
```bash
npx ai-elements@latest add task
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/task.json
```

## Usage
```tsx
import { Task } from '@/components/ai-elements/task';

function ChatMessage({ message }) {
  if (message.tasks) {
    return (
      <div>
        {message.content}
        {message.tasks.map((task, index) => (
          <Task
            key={index}
            task={task}
            onStatusChange={(id, status) => updateTaskStatus(id, status)}
          />
        ))}
      </div>
    );
  }

  return <div>{message.content}</div>;
}
```

## Props
- `task: object` - Task object with id, title, status, etc.
- `onStatusChange?: (id: string, status: string) => void` - Status change handler
- `className?: string` - Additional CSS classes
- `showProgress?: boolean` - Display progress indicator
- `onComplete?: (id: string) => void` - Task completion handler