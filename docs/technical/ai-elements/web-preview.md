# Web Preview Component

## Description
The Web Preview component provides embedded web content preview functionality, allowing users to view web pages, documents, or other web-based content directly within the chat interface.

## Installation
```bash
npx ai-elements@latest add web-preview
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/web-preview.json
```

## Usage
```tsx
import { WebPreview } from '@/components/ai-elements/web-preview';

function ChatMessage({ message }) {
  if (message.type === 'web-preview') {
    return (
      <WebPreview
        url={message.url}
        title={message.title}
        onLoad={() => console.log('Preview loaded')}
        onError={(error) => console.error('Preview error:', error)}
      />
    );
  }

  return <div>{message.content}</div>;
}
```

## Props
- `url: string` - URL to preview
- `title?: string` - Preview title
- `width?: number` - Preview width
- `height?: number` - Preview height
- `onLoad?: () => void` - Load completion handler
- `onError?: (error) => void` - Error handler
- `className?: string` - Additional CSS classes
- `allowFullscreen?: boolean` - Enable fullscreen mode