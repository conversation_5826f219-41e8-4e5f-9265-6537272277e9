# Conversation Component

## Description
The Conversation component is the main container for chat interfaces, providing the overall layout and structure for displaying chat messages and interactions.

## Installation
```bash
npx ai-elements@latest add conversation
```

Or using shadcn CLI:
```bash
npx shadcn@latest add https://registry.ai-sdk.dev/conversation.json
```

## Usage
```tsx
'use client';

import { useChat } from '@ai-sdk/react';
import {
  Conversation,
  ConversationContent,
} from '@/components/ai-elements/conversation';
import {
  Message,
  MessageContent,
} from '@/components/ai-elements/message';
import { Response } from '@/components/ai-elements/response';

export default function Chat() {
  const { messages } = useChat();

  return (
    <Conversation>
      <ConversationContent>
        {messages.map((message, index) => (
          <Message key={index} from={message.role}>
            <MessageContent>
              <Response>{message.content}</Response>
            </MessageContent>
          </Message>
        ))}
      </ConversationContent>
    </Conversation>
  );
}
```

## Props
- `children?: ReactNode` - Child components to render inside the conversation
- `className?: string` - Additional CSS classes
- `maxHeight?: string` - Maximum height of the conversation container