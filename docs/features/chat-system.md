# Chat System

## Features
- Per-user, per-agent chat history.
- Streaming responses with Vercel AI SDK.
- AI Elements for rich UI (artifacts, sources, etc.).
- Authentication protected.

## Database
- `chats`: userId, agentId, title, timestamps.
- `messages`: chatId, role (user/assistant), content, timestamps.

## Flow
1. Load history on mount via `getChatHistory`.
2. User inputs saved before API call.
3. Assistant response saved on finish.
4. New chat created if none exists.

## Customization
- Agent prompts injected as system message.
- Tools can be added to API for specific agents.

## Key Components
- `Conversation`: Chat container.
- `Message`: Individual messages.
- `Input`: Message input with submit.