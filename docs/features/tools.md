# Tools Integration

## Planned Tools
- **Web Search (Exa)**: For UX Research Agent. Integrate via AI SDK tool calling.
- **Document Upload**: Parse PDFs/docs, store temporarily, analyze with AI.
- **Artifact Creation**: Use AI Elements to generate editable documents/responses.
- **Data Synthesis**: Process testing data into insights.

## Implementation
- Define tools in agent config (`src/lib/agents.ts`).
- In `/api/chat/route.ts`, pass tools to `streamText` based on agent.
- Client-side: Handle tool calls in useChat if needed.

## Exa Search Example
Add to env: `EXA_API_KEY`.
Tool function: Search query, return results to model.