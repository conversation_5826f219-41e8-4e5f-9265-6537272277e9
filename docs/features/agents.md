# UX Agents Suite

## Overview
The Leon Suite provides specialized AI agents for UX design workflows. Each agent has unique capabilities, themes, and tools.

## Agents

### UX Research Agent
- **Description**: Conducts user research, analyzes documents, performs web searches, and synthesizes testing data.
- **Tools**: Web search (Exa), document upload, data synthesis, artifact creation.
- **System Prompt**: Data-driven UX research insights.
- **Theme**: Blue (#3B82F6 primary).

### UX Thinking Agent
- **Description**: Examines documents, proposes solutions to UX problems, creates thinking documents.
- **Tools**: Document examination, solution proposal, artifact creation.
- **System Prompt**: Innovative, user-centered design thinking.
- **Theme**: Green (#10B981 primary).

### UX Prototyper Agent
- **Description**: Placeholder for future prototyping features.
- **Tools**: None (future).
- **System Prompt**: Notes prototyping coming soon.
- **Theme**: Purple (#8B5CF6 primary).

## Chat System
- **Persistence**: Messages stored in PostgreSQL via Drizzle ORM (chats and messages tables).
- **History**: Loaded on chat init; saved on user input and assistant response.
- **UI**: Vercel AI Elements (Conversation, Message, Response, Input).
- **API**: `/api/chat` streams responses with agent-specific system prompts using Vercel AI SDK.

## Tools Integration
- **Exa Search**: Planned for UX Research Agent (requires API key).
- **File Uploads**: Planned for document analysis.
- **Artifacts**: Vercel AI Elements for document generation.

## Configuration
Agents defined in `src/lib/agents.ts`. Extend by adding new configs.

## Key Files
- `src/lib/agents.ts`: Agent definitions.
- `src/lib/schema.ts`: DB schema with relations.
- `src/lib/chat.ts`: Chat persistence functions.
- `src/app/chat/[agent]/page.tsx`: Chat UI with history.
- `src/app/api/chat/route.ts`: Streaming API.
- `src/app/dashboard/page.tsx`: Agent selector.