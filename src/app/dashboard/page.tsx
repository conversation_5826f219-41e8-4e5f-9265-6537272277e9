"use client";

import { useSession } from "@/lib/auth-client";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { agents } from "@/lib/agents";
import { Brain, Search, LayoutDashboard } from "lucide-react";

export default function DashboardPage() {
  const { data: session, isPending } = useSession();

  if (isPending) {
    return (
      <div className="flex justify-center items-center h-screen">
        Loading...
      </div>
    );
  }

  if (!session) {
    redirect("/");
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Select an Agent</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {agents.map((agent) => (
          <Card key={agent.id} className="border-2 hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className={`w-12 h-12 ${agent.theme.primary.replace('bg-', 'bg-')} rounded-lg flex items-center justify-center mb-4`}>
                {agent.id === 'ux-research' && <Search className="h-6 w-6 text-blue-600" />}
                {agent.id === 'ux-thinking' && <Brain className="h-6 w-6 text-green-600" />}
                {agent.id === 'ux-prototyper' && <LayoutDashboard className="h-6 w-6 text-purple-600" />}
              </div>
              <CardTitle>{agent.name}</CardTitle>
              <CardDescription>{agent.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href={`/chat/${agent.id}`}>
                <Button className={`${agent.theme.primary} w-full`}>
                  Chat with {agent.name}
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
