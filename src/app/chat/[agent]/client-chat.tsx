"use client";

import { useChat } from '@ai-sdk/react';
import { getAgentById } from '@/lib/agents';
import {
  Conversation,
  ConversationContent,
  ConversationEmptyState,
  ConversationScrollButton
} from '@/components/ai-elements/conversation';
import { Message, MessageContent } from '@/components/ai-elements/message';
import { Response } from '@/components/ai-elements/response';
import {
  PromptInput,
  PromptInputBody,
  PromptInputTextarea,
  PromptInputToolbar,
  PromptInputSubmit
} from '@/components/ai-elements/prompt-input';
import { Suggestions, Suggestion } from '@/components/ai-elements/suggestion';
import { Button } from '@/components/ui/button';
import { ArrowLeft, MessageSquare } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from 'react';
import type { UIMessage } from 'ai';

interface Props {
  initialMessages: UIMessage[];
  agent: ReturnType<typeof getAgentById>;
  agentId: string;
  userId: string;
}

export default function ClientChat({ initialMessages, agent, agentId, userId }: Props) {
  const { messages, sendMessage, status } = useChat();

  const [inputValue, setInputValue] = useState('');
  const [isInitialized, setIsInitialized] = useState(false);

  if (!agent) {
    return <div>Agent not found</div>;
  }

  // Initialize with history messages
  useEffect(() => {
    if (!isInitialized && initialMessages.length > 0) {
      // Add initial messages one by one
      initialMessages.forEach((msg) => {
        sendMessage(msg);
      });
      setIsInitialized(true);
    } else if (!isInitialized && initialMessages.length === 0) {
      // Add welcome message
      sendMessage({
        role: 'assistant',
        parts: [{ type: 'text', text: `Hello! I'm the ${agent.name}. How can I help you with UX today?` }]
      });
      setIsInitialized(true);
    }
  }, [initialMessages, isInitialized, sendMessage, agent.name]);

  // Save assistant message when a new one is added
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.role === 'assistant' && 'content' in lastMessage && typeof lastMessage.content === 'string') {
      fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          action: 'save',
          agentId,
          role: 'assistant',
          content: lastMessage.content,
        }),
      }).catch(console.error);
    }
  }, [messages, agentId, userId]);

  const handlePromptSubmit = async (message: { text?: string; files?: any[] }, e: React.FormEvent) => {
    e.preventDefault();
    const text = message.text?.trim();
    if (!text || !userId) return;

    // Save user message before sending
    try {
      await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          action: 'save',
          agentId,
          role: 'user',
          content: text,
        }),
      });
    } catch (error) {
      console.error('Failed to save user message:', error);
    }

    // Send the message
    await sendMessage({ role: "user", parts: [{ type: "text", text }] });
    setInputValue('');
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
  };

  const hasMessages = messages.length > 0;
  const isLoading = status === 'in_progress';
  const showSuggestions = !hasMessages || (hasMessages && !isLoading);

  return (
    <div className="flex flex-col h-screen">
      <div className="border-b p-4 flex items-center gap-4">
        <Link href="/dashboard">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-xl font-semibold">{agent.name}</h1>
      </div>

      <Conversation className="flex-1">
        <ConversationContent className="p-4">
          {!hasMessages ? (
            <ConversationEmptyState
              title={`Welcome to ${agent.name}`}
              description={agent.description}
              icon={<MessageSquare className="h-8 w-8" />}
            />
          ) : (
            messages.map((m: UIMessage) => (
              <Message key={m.id} from={m.role}>
                <MessageContent>
                  <Response>
                    {m.parts?.map((part: any, i: number) =>
                      part.type === 'text' ? part.text : null
                    ).join('') || ''}
                  </Response>
                </MessageContent>
              </Message>
            ))
          )}
        </ConversationContent>
        <ConversationScrollButton />
      </Conversation>

      {/* Suggestions */}
      {showSuggestions && agent.starterSuggestions.length > 0 && (
        <div className="border-t p-4">
          <Suggestions className="mb-4">
            {agent.starterSuggestions.map((suggestion, index) => (
              <Suggestion
                key={index}
                suggestion={suggestion}
                onClick={handleSuggestionClick}
              >
                {suggestion}
              </Suggestion>
            ))}
          </Suggestions>
        </div>
      )}

      {/* Enhanced Input */}
      <div className="border-t p-4">
        <PromptInput onSubmit={handlePromptSubmit}>
          <PromptInputBody>
            <PromptInputTextarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={`Ask ${agent.name} anything...`}
              disabled={isLoading}
            />
            <PromptInputToolbar>
              <div />
              <PromptInputSubmit status={status} />
            </PromptInputToolbar>
          </PromptInputBody>
        </PromptInput>
      </div>
    </div>
  );
}