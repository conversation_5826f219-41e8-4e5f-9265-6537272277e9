"use client";

import { useChat } from '@ai-sdk/react';
import { getAgentById } from '@/lib/agents';
import {
  Conversation,
  ConversationContent,
  ConversationEmptyState,
  ConversationScrollButton
} from '@/components/ai-elements/conversation';
import { Message, MessageContent } from '@/components/ai-elements/message';
import { Response } from '@/components/ai-elements/response';
import {
  PromptInput,
  PromptInputBody,
  PromptInputTextarea,
  PromptInputToolbar,
  PromptInputSubmit
} from '@/components/ai-elements/prompt-input';
import { Suggestions, Suggestion } from '@/components/ai-elements/suggestion';
import { Actions, Action } from '@/components/ai-elements/actions';
import { Button } from '@/components/ui/button';
import { ArrowLeft, MessageSquare, Copy, RotateCcw } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from 'react';
interface MessagePart {
  type: string;
  text: string;
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content?: string;
  parts?: MessagePart[];
}

interface Props {
  initialMessages: ChatMessage[];
  agent: ReturnType<typeof getAgentById>;
  agentId: string;
  userId: string;
}

export default function ClientChat({ initialMessages, agent, agentId, userId }: Props) {
  const { messages: newMessages, sendMessage, status } = useChat();

  const [inputValue, setInputValue] = useState('');

  // Combine initial messages with new messages
  const allMessages = [...initialMessages, ...newMessages];

  // Save assistant message when a new one is added
  useEffect(() => {
    const lastMessage = newMessages[newMessages.length - 1];
    if (lastMessage && lastMessage.role === 'assistant' && 'content' in lastMessage && typeof lastMessage.content === 'string') {
      fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          action: 'save',
          agentId,
          role: 'assistant',
          content: lastMessage.content,
        }),
      }).catch(console.error);
    }
  }, [newMessages, agentId, userId]);

  if (!agent) {
    return <div>Agent not found</div>;
  }

  const handlePromptSubmit = async (message: any, e: React.FormEvent) => {
    e.preventDefault();
    const text = message.text?.trim();
    if (!text || !userId) return;

    // Save user message before sending
    try {
      await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          action: 'save',
          agentId,
          role: 'user',
          content: text,
        }),
      });
    } catch (error) {
      console.error('Failed to save user message:', error);
    }

    // Send the message
    await sendMessage({ text });
    setInputValue('');
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
  };

  const handleCopyMessage = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const handleRegenerateMessage = async (messageIndex: number) => {
    // Find the last user message before this assistant message
    const userMessages = allMessages.slice(0, messageIndex).filter(m => m.role === 'user');
    const lastUserMessage = userMessages[userMessages.length - 1];

    if (lastUserMessage) {
      const content = (lastUserMessage as any).content ||
        ((lastUserMessage as any).parts && (lastUserMessage as any).parts.map((part: any) =>
          part.type === 'text' ? part.text : ''
        ).join(''));

      if (content) {
        await sendMessage({ text: content });
      }
    }
  };

  const hasMessages = allMessages.length > 0;
  const isLoading = status === 'streaming';
  const showSuggestions = !hasMessages || (hasMessages && !isLoading);

  return (
    <div className="flex flex-col h-screen">
      <div className="border-b p-4 flex items-center gap-4">
        <Link href="/dashboard">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-xl font-semibold">{agent.name}</h1>
      </div>

      <Conversation className="flex-1">
        <ConversationContent className="p-4">
          {!hasMessages ? (
            <ConversationEmptyState
              title={`Welcome to ${agent.name}`}
              description={agent.description}
              icon={<MessageSquare className="h-8 w-8" />}
            />
          ) : (
            allMessages.map((m: any, index: number) => {
              const messageContent = m.content || (m.parts && m.parts.map((part: any) =>
                part.type === 'text' ? part.text : ''
              ).join(''));

              return (
                <Message key={m.id} from={m.role}>
                  <MessageContent>
                    <Response>
                      {messageContent}
                    </Response>
                    {m.role === 'assistant' && messageContent && (
                      <Actions className="mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Action
                          tooltip="Copy message"
                          onClick={() => handleCopyMessage(messageContent)}
                        >
                          <Copy className="h-4 w-4" />
                        </Action>
                        <Action
                          tooltip="Regenerate response"
                          onClick={() => handleRegenerateMessage(index)}
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Action>
                      </Actions>
                    )}
                  </MessageContent>
                </Message>
              );
            })
          )}

          {/* Loading indicator */}
          {isLoading && (
            <Message from="assistant">
              <MessageContent>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                  Thinking...
                </div>
              </MessageContent>
            </Message>
          )}
        </ConversationContent>
        <ConversationScrollButton />
      </Conversation>

      {/* Suggestions */}
      {showSuggestions && agent.starterSuggestions.length > 0 && (
        <div className="border-t p-4">
          <Suggestions className="mb-4">
            {agent.starterSuggestions.map((suggestion, index) => (
              <Suggestion
                key={index}
                suggestion={suggestion}
                onClick={handleSuggestionClick}
              >
                {suggestion}
              </Suggestion>
            ))}
          </Suggestions>
        </div>
      )}

      {/* Enhanced Input */}
      <div className="border-t p-4">
        <PromptInput onSubmit={handlePromptSubmit}>
          <PromptInputBody>
            <PromptInputTextarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={`Ask ${agent.name} anything...`}
              disabled={isLoading}
            />
            <PromptInputToolbar>
              <div />
              <PromptInputSubmit
                status={status}
                disabled={isLoading}
              />
            </PromptInputToolbar>
          </PromptInputBody>
        </PromptInput>
      </div>
    </div>
  );
}