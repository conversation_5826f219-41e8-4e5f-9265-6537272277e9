"use client";

import { useChat } from 'ai/react';
import { getAgentById } from '@/lib/agents';
import { Conversation, ConversationContent } from '@/components/ai-elements/conversation';
import { Message, MessageContent } from '@/components/ai-elements/message';
import { Response } from '@/components/ai-elements/response';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useEffect } from 'react';
import type { UIMessage } from 'ai';

interface Props {
  initialMessages: UIMessage[];
  agent: ReturnType<typeof getAgentById>;
  agentId: string;
  userId: string;
}

export default function ClientChat({ initialMessages, agent, agentId, userId }: Props) {
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: `/api/chat?agent=${agentId}`,
    initialMessages,
  });

  // Save assistant message when a new one is added
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.role === 'assistant' && 'content' in lastMessage && typeof lastMessage.content === 'string') {
      fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          action: 'save',
          agentId,
          role: 'assistant',
          content: lastMessage.content,
        }),
      }).catch(console.error);
    }
  }, [messages, agentId, userId]);

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const text = input.trim();
    if (!text || !userId) return;

    // Save user message before sending
    try {
      await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          action: 'save',
          agentId,
          role: 'user',
          content: text,
        }),
      });
    } catch (error) {
      console.error('Failed to save user message:', error);
    }

    handleSubmit(e);
  };

  return (
    <div className="flex flex-col h-screen">
      <div className="border-b p-4 flex items-center gap-4">
        <Link href="/dashboard">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-xl font-semibold">{agent.name}</h1>
      </div>
      <Conversation className="flex-1">
        <ConversationContent className="p-4">
          {messages.map((m) => (
            <Message key={m.id} from={m.role}>
              <MessageContent>
                <Response>
                  {Array.isArray(m.content) ? m.content.map((part, i) => part.type === 'text' ? part.text : null) : m.content}
                </Response>
              </MessageContent>
            </Message>
          ))}
        </ConversationContent>
        <form onSubmit={handleFormSubmit} className="border-t p-4">
          <Input
            value={input}
            onChange={handleInputChange}
            placeholder="Type your message..."
            disabled={isLoading}
          />
        </form>
      </Conversation>
    </div>
  );
}