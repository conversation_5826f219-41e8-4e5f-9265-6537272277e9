import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import { getAgentById } from '@/lib/agents';
import { getChatHistory } from '@/lib/chat';
import { auth } from '@/lib/auth';
import ClientChat from './client-chat';

interface Props {
  params: Promise<{ agent: string }>;
}

export default async function ChatPage({ params }: Props) {
  const resolvedParams = await params;
  const session = await auth.api.getSession({ headers: await headers() });
  if (!session) {
    redirect('/');
  }

  const agent = getAgentById(resolvedParams.agent);
  if (!agent) {
    redirect('/dashboard');
  }

  const history = await getChatHistory(session.user.id, resolvedParams.agent);

  const initialMessages = history.map(msg => ({
    id: msg.id.toString(),
    role: msg.role as 'user' | 'assistant',
    content: [{ type: 'text', text: msg.content }],
  } as const));

  if (initialMessages.length === 0) {
    initialMessages.push({
      id: 'starter',
      role: 'assistant',
      content: [{ type: 'text', text: `Hello! I'm the ${agent.name}. How can I help you with UX today?` }],
    } as const);
  }

  return (
    <ClientChat
      initialMessages={initialMessages}
      agent={agent}
      agentId={resolvedParams.agent}
      userId={session.user.id}
    />
  );
}
