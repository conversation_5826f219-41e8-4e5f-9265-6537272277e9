"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SignInButton } from "@/components/auth/sign-in-button";
import { Brain, Search, LayoutDashboard } from "lucide-react";

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl md:text-6xl font-bold tracking-tight mb-6">
            Leon Suite
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Empower your UX design process with specialized AI agents. From research to prototyping, our agents help you build better user experiences faster.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <SignInButton className="text-lg px-8">
              Get Started
            </SignInButton>
            <Button variant="outline" size="lg" className="text-lg px-8">
              Learn More
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Our UX Agents</h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="border-blue-200">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Search className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>UX Research Agent</CardTitle>
                <CardDescription>
                  Conduct in-depth user research, analyze documents, perform web searches, and synthesize insights.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Web search with Exa</li>
                  <li>• PDF and document analysis</li>
                  <li>• Testing data synthesis</li>
                  <li>• Research document generation</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-green-200">
              <CardHeader>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <Brain className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle>UX Thinking Agent</CardTitle>
                <CardDescription>
                  Tackle complex UX challenges with creative thinking, document examination, and solution proposals.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Document examination</li>
                  <li>• UX problem solving</li>
                  <li>• Solution brainstorming</li>
                  <li>• Thinking documents with artifacts</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-purple-200">
              <CardHeader>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <LayoutDashboard className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle>UX Prototyper Agent</CardTitle>
                <CardDescription>
                  Prototype your UX designs efficiently (Coming soon).
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">Future feature for rapid prototyping and wireframing.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your UX Workflow?</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Join thousands of designers using AI to create exceptional user experiences.
          </p>
          <SignInButton className="text-lg px-8 bg-blue-600 hover:bg-blue-700">
            Start Your Free Trial
          </SignInButton>
        </div>
      </section>
    </main>
  );
}