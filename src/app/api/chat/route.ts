import { openai } from '@ai-sdk/openai';
import { streamText, convertToCoreMessages } from 'ai';
import { auth } from '@/lib/auth';
import { getAgentById } from '@/lib/agents';
import { saveMessage, getChatHistory } from '@/lib/chat';

export const runtime = 'edge';

export async function POST(req: Request) {
  const session = await auth.api.getSession({ headers: await req.headers });
  if (!session) {
    return new Response('Unauthorized', { status: 401 });
  }

  const body = await req.json();
  const { messages } = body;

  const url = new URL(req.url);
  const agentId = url.searchParams.get('agent');
  const agent = agentId ? getAgentById(agentId) : null;

  const coreMessages = convertToCoreMessages(messages);
  if (agent) {
    coreMessages.unshift({
      role: 'system',
      content: agent.systemPrompt,
    });
  }

  const result = await streamText({
    model: openai(process.env.OPENAI_MODEL || 'gpt-4o-mini'),
    messages: coreMessages,
  });

  return result.toTextStreamResponse();
}

export async function GET(req: Request) {
  const session = await auth.api.getSession({ headers: await req.headers });
  if (!session) {
    return new Response('Unauthorized', { status: 401 });
  }

  const url = new URL(req.url);
  const agentId = url.searchParams.get('agent');
  if (!agentId) {
    return new Response('Agent ID required', { status: 400 });
  }

  // For GET, return history
  // But since we load server-side, this might not be needed, but keep for compatibility
  const history = await getChatHistory(session.user.id, agentId);
  const uiMessages = history.map(msg => ({
    id: msg.id.toString(),
    role: msg.role,
    content: msg.content,
  }));

  return Response.json({ messages: uiMessages });
}

export async function OPTIONS() {
  return new Response('ok', {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}