

export interface AgentConfig {
  id: string;
  name: string;
  description: string;
  theme: {
    primary: string;
    secondary: string;
    accent: string;
  };
  systemPrompt: string;
  tools: string[]; // List of tool IDs available to this agent
  starterSuggestions: string[];
}

export const agents: AgentConfig[] = [
  {
    id: 'ux-research',
    name: 'UX Research Agent',
    description: 'Conducts user research, analyzes documents, performs web searches, and synthesizes testing data.',
    theme: {
      primary: 'bg-blue-500',
      secondary: 'bg-blue-600',
      accent: 'bg-blue-700',
    },
    systemPrompt: `You are a UX Research Agent. You help with user research by analyzing uploaded documents and PDFs, performing web searches using Exa, synthesizing testing data, and creating research documents with artifacts. Always provide insightful, data-driven UX research insights.`,
    tools: ['web-search', 'document-upload', 'synthesize-data', 'create-artifact'],
    starterSuggestions: [
      'Research best practices for mobile UX in e-commerce apps',
      'Analyze this user feedback document for pain points',
      'Search for recent UX trends in accessibility',
      'Synthesize A/B testing results from uploaded data'
    ]
  },
  {
    id: 'ux-thinking',
    name: 'UX Thinking Agent',
    description: 'Examines documents, proposes solutions to complex UX problems, and creates thinking documents with artifacts.',
    theme: {
      primary: 'bg-green-500',
      secondary: 'bg-green-600',
      accent: 'bg-green-700',
    },
    systemPrompt: `You are a UX Thinking Agent. You examine uploaded documents, propose creative solutions to complex UX challenges, and generate thinking documents with artifacts. Focus on innovative, user-centered design thinking.`,
    tools: ['document-upload', 'propose-solutions', 'create-artifact'],
    starterSuggestions: [
      'Propose UX improvements for a cluttered dashboard interface',
      'Examine this wireframe for usability issues',
      'Brainstorm solutions for user onboarding flow',
      'Create a thinking document on information architecture'
    ]
  },
  {
    id: 'ux-prototyper',
    name: 'UX Prototyper Agent',
    description: 'Future feature for prototyping UX designs. Currently a placeholder.',
    theme: {
      primary: 'bg-purple-500',
      secondary: 'bg-purple-600',
      accent: 'bg-purple-700',
    },
    systemPrompt: `You are a UX Prototyper Agent. This is a placeholder for future prototyping capabilities. Respond by noting that prototyping features are coming soon.`,
    tools: [],
    starterSuggestions: [
      'Prototype a new login flow (coming soon)',
      'Generate wireframes for dashboard (future feature)'
    ]
  }
];

export const getAgentById = (id: string): AgentConfig | undefined => {
  return agents.find(agent => agent.id === id);
};
