import { db } from './db';
import { chats, messages, chatsRelations } from './schema';
import { eq, and } from 'drizzle-orm';
import type { Message as DBMessage } from './schema';

export async function getOrCreateChat(userId: string, agentId: string, title?: string) {
  let chat = await db.query.chats.findFirst({
    where: and(eq(chats.userId, userId), eq(chats.agentId, agentId)),
  });

  if (!chat) {
    [chat] = await db.insert(chats).values({
      userId,
      agentId,
      title: title || 'New Chat',
    }).returning();
  }

  return chat;
}

export async function getChatHistory(userId: string, agentId: string): Promise<DBMessage[]> {
  const chat = await getOrCreateChat(userId, agentId);
  const history = await db.query.messages.findMany({
    where: eq(messages.chatId, chat.id),
    orderBy: messages.createdAt,
  });
  return history;
}

export async function saveMessage(userId: string, agentId: string, role: 'user' | 'assistant', content: string, title?: string) {
  const chat = await getOrCreateChat(userId, agentId, title);
  await db.insert(messages).values({
    chatId: chat.id,
    role,
    content,
  });
}